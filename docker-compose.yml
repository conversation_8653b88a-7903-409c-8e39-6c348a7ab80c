version: '3.8'

services:
  # Default portfolio service (production-ready)
  portfolio:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    ports:
      - '${PORT:-8080}:8080'
    environment:
      - NODE_ENV=production
      - VITE_BUILD_SOURCEMAP=false
      - VITE_DEV_TOOLS=false
    env_file:
      - .env
    networks:
      - portfolio-network
    restart: unless-stopped
    healthcheck:
      test:
        [
          'CMD',
          'wget',
          '--no-verbose',
          '--tries=1',
          '--spider',
          '--timeout=5',
          'http://localhost:8080/health',
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    security_opt:
      - no-new-privileges:true
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

networks:
  portfolio-network:
    driver: bridge
    name: portfolio-network
